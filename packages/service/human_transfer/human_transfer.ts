import logger from 'model/logger/logger'
import { ChatDB, IChat } from '../database/chat'
import { ChatStateStore } from '../local_cache/chat_state_store'
import { Config } from 'config'
import { EventTracker, IEventType } from 'model/logger/data_driven'
import { JuziAPI } from 'model/juzi/api'
import { IWecomMsgType } from 'model/juzi/type'

export const HumanTransferType = {
  // general
  UnknownMessageType: '客户发了一个文件',
  NotBindPhone: '客户手机号绑定失败',
  ProblemSolving: '客户遇到问题',
  FailedToJoinGroup: '客户拉群失败',
  ProcessImage: '客户发了一张【图片】',
  MessageSendFailed: '消息发送失败',
  ReaskAnotherDay: '客户次日被主动提醒支付',
  HesitatePayment: '客户支付犹豫',
  VoiceOrVideoCall: '客户发起语音/视频通话',
  RobotDetected: '客户识别到了AI',
  PaidCourse: '[烟花]客户已支付[烟花]',
  ProcessVideo: '客户发了一个【视频】',
  ProcessVideoFailed: '客户发了一个【视频】，识别失败',

  // moer_overseas

  // yuhe
  ExecutePostpone: '客户已延期',
  ExecuteRetrain: '客户已复训',

  // haogu

} as const

/**
 * 通知发送提供器接口。允许通过不同实现发送通知（默认使用 JuziAPI）。
 */
export interface NotificationProvider {
  notify: (message: string, options?: NotifyOptions) => Promise<void>
}

/**
 * 通知可选参数。
 */
export interface NotifyOptions {
  imBotId?: string
  groupId?: string
}

/**
 * 默认通知实现：通过 JuziAPI 发送群消息。
 */
export class JuziNotificationProvider implements NotificationProvider {
  async notify(message: string, options?: NotifyOptions): Promise<void> {
    const imBotId = options?.imBotId ?? (Config.setting.wechatConfig?.id as string)
    const groupId = options?.groupId ?? (Config.setting.wechatConfig?.notifyGroupId as string)

    await JuziAPI.sendGroupMsg(imBotId, groupId, {
      type: IWecomMsgType.Text,
      text: message
    })
  }
}

export class BaseHumanTransfer {
  chatDBClient:ChatDB<IChat>
  chatStateStoreClient:ChatStateStore
  private notifier: NotificationProvider

  /**
   * @param chatDBClient 聊天库
   * @param chatStateStoreClient 聊天状态缓存
   * @param notifier 可选：通知发送实现（默认 JuziNotificationProvider）
   */
  constructor(chatDBClient:ChatDB<IChat>, chatStateStoreClient:ChatStateStore, notifier: NotificationProvider = new JuziNotificationProvider()) {
    this.chatDBClient = chatDBClient
    this.chatStateStoreClient = chatStateStoreClient
    this.notifier = notifier
  }

  /**
   *
   * 转交人工；toHuman 为 true 时，表示需要人工接入；'onlyNotify' 表示只通知不切换状态。
   * 如需自定义通知方式，请在构造函数中注入自定义 NotificationProvider。
   * @param chatId
   * @param userId
   * @param notifyMessage
   * @param toHuman
   * @param imBotId
   * @param notifyFunction
   */
  public async transfer(
    chatId: string,
    userId: string,
    notifyMessage: string,
    toHuman: boolean | 'onlyNotify' = true,
    imBotId?: string,
    // Deprecated: 外部不再需要传入函数，请注入 NotificationProvider。
    notifyFunction?: () => Promise<void>
  ) {
    const chat = await this.chatDBClient.getById(chatId) as IChat
    if (typeof toHuman === 'boolean' && chat) {
      await this.chatDBClient.setHumanInvolvement(chatId, toHuman)
    } else {
      if (!chat) {
        const currentSender = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, userId)
        await this.chatDBClient.create({
          id: chatId,
          round_ids: [],
          contact: {
            wx_id: userId,
            wx_name: currentSender ? currentSender.name : userId,
          },
          wx_id: Config.setting.wechatConfig?.id as string,
          created_at: new Date(),
          chat_state: await this.chatStateStoreClient.get(chatId)
        })
      }
    }

    // 包装通知
    let contactName = userId
    let notificationMessage: string
    const isPaid = chat?.chat_state?.state?.is_complete_payment ?? false
    if (chat && chat.contact && chat.contact.wx_name) {
      contactName = chat.contact.wx_name
    }
    notificationMessage = `${contactName} ${isPaid ? '💰' : ''}${notifyMessage}`

    if (toHuman === true) { notificationMessage += '\n[心碎]AI已关闭[心碎]' }
    logger.log({ chat_id: chatId }, '通知人工接入：', notificationMessage)
    if (toHuman === 'onlyNotify' && Config.setting.localTest) return

    // 优先兼容旧参数；否则走注入的 Notifier
    if (notifyFunction) {
      await notifyFunction()
      return
    }

    await this.notifier.notify(notificationMessage, imBotId ? { imBotId } : undefined)
  }
}

export type TransferKey = string | number

export interface HumanTransferConfig<T extends TransferKey> {
  /** 转交原因文案映射 */
  transferMessage: Record<T, string>
  /** 事件埋点 */
  eventTracker: EventTracker
  /** 实际执行转交的客户端（可以预置不同的 Notifier） */
  humanTransferClient: BaseHumanTransfer
}

export type TransferMode = true | false | 'onlyNotify'


export class HumanTransfer<T extends TransferKey> {
  private cfg: HumanTransferConfig<T>
  constructor(cfg: HumanTransferConfig<T>) {
    this.cfg = cfg
  }

  public async transfer(
    chatId: string,
    userId: string,
    transferType: T,
    toHuman: TransferMode = true,
    additionalMsg?: string
  ) {
    if (userId === 'null') {
      logger.error('[HumanTransfer] userId is null', transferType)
      return
    }

    this.cfg.eventTracker.track(chatId, IEventType.TransferToManual, { reason: this.cfg.transferMessage[transferType] })

    // 拼接要发送的消息
    const handleType = toHuman === true ? '请人工处理' : '请观察👀'

    const message = `${ this.cfg.transferMessage[transferType] as string }，${ handleType }${ additionalMsg ? `\n${additionalMsg}` : ''}`
    return await this.cfg.humanTransferClient.transfer(chatId, userId, message, toHuman)
  }
}
