{"name": "moer_material_extractor", "private": true, "scripts": {"tsc-check": "tsc --noEmit", "prisma_generate": "cd prisma && pnpm dlx prisma generate"}, "devDependencies": {"@types/express": "^4.17.19", "@types/jest-when": "^3.5.5", "@types/node": "^20.7.0", "jest": "^29.7.0", "jest-when": "^3.7.0", "prisma": "^6.13.0", "ts-node": "^10.9.1", "typescript": "5.8.2"}, "dependencies": {"@prisma/client": "^6.13.0", "axios": "^1.5.1", "config": "workspace:*", "dayjs": "^1.11.13", "express": "^4.18.2", "lib": "workspace:*", "model": "workspace:*", "service": "workspace:*"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}