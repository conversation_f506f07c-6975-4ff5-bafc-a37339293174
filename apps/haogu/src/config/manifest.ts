import { defineApp } from 'service/appkit/types'

/**
 * 定义 客户状态，是否完课，是否下单，是否完成作业等等
 */
export interface IChattingFlag {
  [key: string]: boolean | string | number | undefined

  is_add_big_plan_tasks?: boolean // 是否添加大 Planner 每日定时任务
  is_complete_payment?: boolean
  after_bonding?: boolean
  is_send_core_materials?: boolean // 是否已经发送筹码峰核心资料
  is_send_installation_guide?: boolean // 是否已经发送安装教程
  is_welcome_stage_completed?: boolean // 欢迎语阶段是否完成
  is_friend_accepted?: boolean
  is_finish_homework_day1?: boolean
  is_finish_homework_day2?: boolean
  is_finish_homework_day3?: boolean
  is_finish_homework_day4?: boolean
  is_finish_homework_day5?: boolean
  is_finish_homework_day6?: boolean
  is_attend_course_day1?: boolean
  is_attend_course_day2?: boolean
  is_attend_course_day3?: boolean
  is_attend_course_day4?: boolean
  is_attend_course_day5?: boolean
  is_attend_course_day6?: boolean
  is_complete_course_day1?: boolean
  is_complete_course_day2?: boolean
  is_complete_course_day3?: boolean
  is_complete_course_day4?: boolean
  is_complete_course_day5?: boolean
  is_complete_course_day6?: boolean
  is_tool_used?: boolean // 好人好股软件使用情况
  is_attend_preview_course_1?: boolean // 福利课1-双线合一观看情况
  is_attend_preview_course_2?: boolean // 福利课2-四点共振观看情况
  is_attend_preview_course_3?: boolean // 福利课3-主力锁仓观看情况
  is_benefit_claimed_day2?: boolean // 第二课课程福利领取情况
  is_benefit_claimed_day3?: boolean // 第三课课程福利领取情况
  is_benefit_claimed_day4?: boolean // 第四课9.9优惠券领取情况
  is_benefit_claimed_day5?: boolean // 第五课9.9优惠券领取情况
  live_watch_ratio_day1?: number // 第一课直播观看比例
  live_watch_ratio_day2?: number // 第二课直播观看比例
  live_watch_ratio_day3?: number // 第三课直播观看比例
  live_watch_ratio_day4?: number // 第四课直播观看比例
  live_watch_ratio_day5?: number // 第五课直播观看比例
  live_watch_ratio_day6?: number // 第六课直播观看比例
  is_watched_back_course_day1?: boolean // 第一课录播观看情况
  is_watched_back_course_day2?: boolean // 第二课录播观看情况
  is_watched_back_course_day3?: boolean // 第三课录播观看情况
  is_watched_back_course_day4?: boolean // 第四课录播观看情况
  is_watched_back_course_day5?: boolean // 第五课录播观看情况
  is_watched_back_course_day6?: boolean // 第六课录播观看情况
}

/**
 * 对通用层注入变量
 */
export const manifest = defineApp({
  projectName: 'haogu',
  // agentName: '小钱助教',

  // 槽位提取
  extractUserSlots: {
    topicRecommendations: `- 基本信息：称呼、客户年龄，性别，所在地，职业
- 投资基础：资金情况（资金规模，投资占比），炒股经验，投资风格（风险偏好）
- 交易现状与问题：当前交易现状（盈亏情况），最大痛点（最困扰的问题，如选股困难，频繁亏损）,学习动机，期待目标，失败经历
- 决策影响因素：决策者角色（个人决策还是需要家庭成员朋友共同决策），决策关注点（如关注课程价值点，如价格、师资、方法可靠性、客户案例）,异议点（例如价格敏感、时间紧张、担忧自己学不会、信任感不足）`, // 当前项目主要关注的槽位
    topicRules: `- 不应提取出关于课程的时间信息或者对话日期等基础时间信息。
- 基本信息::称呼：如果客户发送的称呼是自己的人名，禁止将客户原名提取出来，你需要结合客户的性别将称呼改为姓加“哥”，姓加“姐”` // 额外的提取规则说明
  },

  // Kafka 配置
  kafka: {
    brokers: [
      'alikafka-serverless-cn-db14e5zfm02-1000.alikafka.aliyuncs.com:9093',
      'alikafka-serverless-cn-db14e5zfm02-2000.alikafka.aliyuncs.com:9093',
      'alikafka-serverless-cn-db14e5zfm02-3000.alikafka.aliyuncs.com:9093'
    ],
    sasl: {
      username: 'alikafka_serverless-cn-db14e5zfm02',
      password: 'UgOTLdDdbyDctbSBT9TnaVb8h5Xw4Xu8',
      mechanism: 'plain'
    },
    clientId: 'haogu-kafka-client',
    groupId: 'haogu-kafka-group',
    ssl: {
      rejectUnauthorized: false
    },
    connectionTimeout: 10000,
    requestTimeout: 30000,
    topics: {
      scrmWorkToAiStaff: 'scrm_work_to_ai_staff',
      scrmMessageToAiStaff: 'scrm_message_to_ai_staff',
      srcmReadMarkToAiStaff: 'scrm_readMark_to_ai_staff',
      scrmCrmOrderPlacedToAiStaff : 'scrm_crm_order_placed_to_ai_staff'
    }
  }
})