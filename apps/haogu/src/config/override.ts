import dayjs from 'dayjs'
import { AsyncLock } from 'model/lock/lock'
import { defineOverride } from 'service/appkit/types'
import { startTasks } from 'service/visualized_sop/visualized_sop_task_starter'
import { calTaskTime } from '../visualized_sop/visualized_sop_processor'
import { chatDBClient, chatStateStoreClient } from './instance/base_instance'
import { commonMessageSender } from './instance/send_message_instance'
import { eventTrackClient } from './instance/event_track_instance'
import { extractUserSlots } from './instance/instance'
import { ObjectUtil } from 'lib/object'
import { IChattingFlag, manifest } from './manifest'
import { HumanTransferType } from 'service/human_transfer/human_transfer'
import {
  ScrmCrmEventToAiStaff,
  ScrmCrmOrderEvent,
  ScrmCrmYellowCartEvent,
  ScrmCustomerEventToAiStaff,
  ScrmDDMAppLogin,
  ScrmDDMBroadcastEvent,
  ScrmDDMEventToAiStaff,
  ScrmLinkReadMarkToAi<PERSON>taff,
  ScrmReadMarkToAiStaff,
  ScrmWorkToAiStaff
} from 'model/haogu/callback/type'
import logger from 'model/logger/logger'
import { PrismaMongoClient } from '../database/prisma'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from '../workflow/helper/register_task'
import OpenAI from 'openai'
import axios from 'axios'
import { IWecomReceivedMsgType } from 'model/juzi/type'
import { IEventType } from 'model/logger/data_driven'
import { Config } from 'config'
import { BigPlanner } from '../planner/daily_plan/big_planner'
import { DataService } from '../workflow/helper/get_data'
import { HWLink } from './homework_link'

const MAX_VIDEO_SIZE = 150 * 1024 * 1024 // 150MB

/**
 * 对通用层注入函数
 */
export const override =  defineOverride({
  async onInit() {
    // 启动后钩子（可留空）
  },

  // 加好友后发送欢迎语
  async sendWelcomeMessage(chatId, userId) {
    const lock = new AsyncLock()
    await lock.acquire(chatId, async () => { // 如果有新消息，当前回复会被丢弃
      const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
      if (flags.is_friend_accepted) {
        logger.log(`已经添加了好友,chat_id:${chatId}`)
        return
      } else {
        await chatStateStoreClient.update(chatId, {
          state: {
            is_friend_accepted: true
          }
        })
      }

      const chat = await chatDBClient.getById(chatId)

      if (chat) {
        extractUserSlots.extractUserSlotsFromChatHistory({ chatId, chatHistory:[{
          role: 'user',
          date: dayjs().format('YYYY-MM-DD'),
          message: `我的名字是${chat.contact.wx_name}`
        }], topicRecommendations: extractUserSlots.getTopicRecommendations(), topicRules:extractUserSlots.getTopicRules() })
      }

      await startTasks(chatStateStoreClient, manifest.projectName, userId, chatId, calTaskTime)

      await commonMessageSender.sendText(chatId, {
        text: '[愉快]您好呀，很开心认识您，恭喜加入《筹码主升训练营》，我是您的专属助教\n' +
            '接下来由我陪您学习，助您建立属于自己完整的交易体系! 老师赠送的6节筹码课程在明天晚上19:20微信直播，连续6天，记得置顶小助教微信\n' +
            ' ------------------- \n' +
            '❤您现在有空吗？我来给你发筹码峰福利课资料：三大适用技巧',
        description: '欢迎语'
      })

      // 启动客户沉默5分钟后的提醒任务
      await SilentReAsk.schedule(
        TaskName.SendCoreMaterials,
        chatId,
        5 * 60 * 1000, // 5分钟
        5,
        { auto_retry: true, independent: false }
      )

      BigPlanner.addTasks(chatId, await DataService.getCourseStartTimeByChatId(chatId))
    }, { timeout: 2 * 60 * 1000 })
  },

  async handleUnknownMessage(message) {

  },

  async handleImageMessage(imageUrl, chatId) {
    return `【图片URL】${imageUrl}`
  },


  async handleVideoMessage(videoUrl: string, chatId: string) {
    const dashscopeApiKey = Config.setting.qwen.apiKey || process.env.DASHSCOPE_API_KEY

    const openai = new OpenAI({
      apiKey: dashscopeApiKey,
      baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    })

    try {
      const sizeBytes = await getVideoFileSize(videoUrl)
      if (sizeBytes !== null && sizeBytes > MAX_VIDEO_SIZE) {
        throw new Error(`视频大小 ${ (sizeBytes / (1024 * 1024)).toFixed(2) } MB 超过 150 MB 限制`)
      }
      const messages: any = [
        {
          'role': 'user',
          'content': [{
            'type': 'video_url',
            'video_url': { 'url': videoUrl },
          },
          { 'type': 'text', 'text': '请以【视频】开头，然后分析视频的内容是什么，输出一段话，请不要使用markdown格式' }]
        }]
      const qwenResponse = await openai.chat.completions.create({
        model: 'qwen-omni-turbo',
        messages: messages,
        max_completion_tokens: 512,
        stream: true,
        stream_options: {
          include_usage: true
        },
        modalities: ['text']
      })
      let qwenResponseText = ''
      for await (const chunk of qwenResponse) {
        qwenResponseText += chunk.choices[0]?.delta.content || ''
      }
      qwenResponseText = qwenResponseText.trim()
      if (!qwenResponseText.startsWith('【视频】')) { qwenResponseText = `【视频】${qwenResponseText}` }
      qwenResponseText = qwenResponseText.replace(/\n/g, '')

      // await YuHeHumanTransfer.transfer(chatId, userId, YuHeHumanTransferType.ProcessVideo, 'onlyNotify', qwenResponseText)
      eventTrackClient.track(chatId, IEventType.TransferToManual, {
        reason: ObjectUtil.enumValueToKey(
          HumanTransferType,
          HumanTransferType.UnknownMessageType
        ),
        video_url: videoUrl,
        msg_type: ObjectUtil.enumValueToKey(
          IWecomReceivedMsgType,
          IWecomReceivedMsgType.Video
        ),
      })

      return qwenResponseText
    } catch (error) {
      logger.warn(`处理视频消息时出错: ${error}`)
      eventTrackClient.track(chatId, IEventType.TransferToManual, {
        reason: ObjectUtil.enumValueToKey(HumanTransferType, HumanTransferType.UnknownMessageType),
        video_url: videoUrl,
        msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, IWecomReceivedMsgType.Video),
      })
      return ''
    }
  },

  // 完成作业
  async handleFinishWork(data: { chat_id: string } & ScrmWorkToAiStaff) {
    logger.log(`[handleFinishWork] 开始处理作业完成，chat_id: ${data.chat_id}, link: ${data.link}, status: ${data.status}, score: ${data.score}`)

    // 更新作业完成状态
    if (data.status != 1) {
      return
    }
    if (data.link == HWLink.day1) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day1:true
        }
      })
    } else if (data.link == HWLink.day2) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day2:true
        }
      })
    } else if (data.link == HWLink.day3) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day3:true
        }
      })
    } else if (data.link == HWLink.day4) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day4:true
        }
      })
    } else if (data.link == HWLink.day5) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day5:true
        }
      })
    } else if (data.link == HWLink.day6) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day6:true
        }
      })
    } else {
      return
    }

    // 根据分数处理不同逻辑
    if (data.score === 100) {
      // 满分：1分钟后发送鼓励消息
      await SilentReAsk.schedule(
        TaskName.SendPerfectEncourageMsg,
        data.chat_id,
        90 * 1000, // 1分钟
        { score: data.score, link: data.link },
        { auto_retry: true, independent: true }
      )
    } else {
      // 非满分：立即发送鼓励消息和作业解析视频链接
      await SilentReAsk.schedule(
        TaskName.SendHomeworkAnalysis,
        data.chat_id,
        90 * 1000, // 1秒后发送，避免与其他消息冲突
        { score: data.score, link: data.link },
        { auto_retry: true, independent: true }
      )
    }
  },
  // 读消息
  async handleReadMessag(data:ScrmReadMarkToAiStaff) {
    const chatId = `${data.custUnifiedUserId}_${data.staffId}`
    const mongoClient = PrismaMongoClient.getInstance()
    await mongoClient.chat_history.updateMany({
      where:{
        chat_id:chatId
      },
      data:{
        is_read:true
      }
    })
  },
  //读链接
  async handleReadLink(data:ScrmLinkReadMarkToAiStaff) {

  },
  // 新客户
  async handleNewCustomer(data:ScrmCustomerEventToAiStaff) {
    console.log(data)
    if (data.status == 9 || data.status == 2057 || data.status == 2313) {
      //添加
      const chatId = `${data.custUnifiedUserId}_${data.staffId}`
      const mongoClient = PrismaMongoClient.getInstance()
      await mongoClient.chat.upsert({ where:{ id:chatId }, create:{
        id:chatId,
        contact:{
          wx_id:data.custUnifiedUserId,
          wx_name:data.custNickname
        },
        wx_id:String(data.staffId),
        created_at:new Date(),
        chat_state:{
          nodeInvokeCount:{},
          nextStage:'free_talk',
          userSlots:{},
          state:{}
        },
        course_no:Number(dayjs().format('YYYYMMDD')),
        conversation_id:data.conversationId,
        customer_tool_user_id:data.custToolUserId,
        wx_union_id:data.custUnionId
      }, update:{
        is_deleted:false
      } })
      await this.sendWelcomeMessage(chatId, data.custUnifiedUserId)
    } else if (data.status == 0 || data.status == 8 || data.status == 2049) {
      //删除
      const chatId = `${data.custUnifiedUserId}_${data.staffId}`
      const mongoClient = PrismaMongoClient.getInstance()
      await mongoClient.chat.update({ where:{
        id:chatId
      }, data:{
        is_deleted:true
      } })
    } else {
      logger.error('新客户接口未知status', data)
    }
  },
  async handleCrmEvent(data:{chat_id:string} & ScrmCrmEventToAiStaff) {
    if (data.msgType == 10001) {
      await this.handleOrder(data as { chat_id: string } & ScrmCrmOrderEvent)
    } else if (data.msgType == 10002) {
      await this.handleYellowCartEnterReceivePage(data as {chat_id:string} & ScrmCrmYellowCartEvent)
    } else if (data.msgType == 10003) {
      await this.handleYellowCartBuy(data as {chat_id:string} & ScrmCrmYellowCartEvent)
    } else {
      logger.error(`chat_id: ${data.chat_id} handle crm event occur unknow msgType ${data.msgType}`)
    }
  },
  // 完成订单
  async handleOrder(data: {chat_id:string} & ScrmCrmOrderEvent) {
    await chatStateStoreClient.update(data.chat_id, {
      state:<IChattingFlag>{
        is_complete_payment:true
      }
    })
  },
  async handleYellowCartEnterReceivePage(data: {chat_id:string} & ScrmCrmYellowCartEvent) {
    logger.log(`chat_id:${data.chat_id} enter yellow cart receive page`)
  },
  async handleYellowCartBuy(data: {chat_id:string} & ScrmCrmYellowCartEvent) {
    logger.log(`chat_id:${data.chat_id} buy yellow cart`)
  },
  async handleDDMEvent(data:{chat_id:string} & ScrmDDMEventToAiStaff) {
    if (data.msgType == 1) {
      this.handleFinishWork(data as {chat_id:string} & ScrmWorkToAiStaff)
    } else if (data.msgType == 3) {
      this.handleOnline(data as {chat_id:string} & ScrmDDMBroadcastEvent)
    } else if (data.msgType == 4) {
      this.handleOffline(data as {chat_id:string} & ScrmDDMBroadcastEvent)
    } else if (data.msgType == 6) {
      this.handleLoginPC(data as {chat_id:string} & ScrmDDMAppLogin)
    } else if (data.msgType == 7) {
      this.handleLoginApp(data as {chat_id:string} & ScrmDDMAppLogin)
    } else {
      logger.error(`chat_id: ${data.chat_id} handle ddm event occur unknow msgType ${data.msgType}`)
    }
  },
  // 直播上线
  async handleOnline(data:{chat_id:string} & ScrmDDMBroadcastEvent) {
    logger.log(`chat_id: ${data.chat_id} 直播上线 data:${JSON.stringify(data)}`)

    // 处理预习课到课事件记录
    const courseIdMap: Record<string, keyof IChattingFlag> = {
      '6507467': 'is_attend_preview_course_1', // 预习1、双线合一
      '6507475': 'is_attend_preview_course_2', // 预习2、四点共振
      '6507480': 'is_attend_preview_course_3'  // 预习3、锁仓破仓
    }

    const stateKey = courseIdMap[data.courseId]
    if (stateKey) {
      try {
        await chatStateStoreClient.update(data.chat_id, {
          state: { [stateKey]: true } as Partial<IChattingFlag>
        })
        logger.log(`chat_id: ${data.chat_id} 更新预习课出席状态: ${stateKey} = true`)
      } catch (error) {
        logger.error(`chat_id: ${data.chat_id} 更新预习课出席状态失败: ${error}`)
      }
    }
  },
  // 直播下线
  async handleOffline(data:{chat_id:string} & ScrmDDMBroadcastEvent) {
    logger.log(`chat_id: ${data.chat_id} 直播下线 data:${JSON.stringify(data)}`)
  },
  //首次登陆pc
  async handleLoginPC(data:{chat_id:string} & ScrmDDMAppLogin) {
    logger.log(`chat_id: ${data.chat_id} 首次登陆pc data:${JSON.stringify(data)}`)
    const realChatId = `${data.unifiedUserId}_${data.staffId}`

    try {
      await chatStateStoreClient.update(realChatId, {
        state: { is_tool_used: true } as Partial<IChattingFlag>
      })
      logger.log(`chat_id: ${realChatId} 更新工具使用状态: is_tool_used = true (首次登陆PC)`)
    } catch (error) {
      logger.error(`chat_id: ${realChatId} 更新工具使用状态失败: ${error}`)
    }
  },
  //首次登陆app
  async handleLoginApp(data:{chat_id:string} & ScrmDDMAppLogin) {
    logger.log(`chat_id: ${data.chat_id} 首次登陆app data:${JSON.stringify(data)}`)
    const realChatId = `${data.unifiedUserId}_${data.staffId}`

    try {
      await chatStateStoreClient.update(realChatId, {
        state: { is_tool_used: true } as Partial<IChattingFlag>
      })
      logger.log(`chat_id: ${realChatId} 更新工具使用状态: is_tool_used = true (首次登陆APP)`)
    } catch (error) {
      logger.error(`chat_id: ${realChatId} 更新工具使用状态失败: ${error}`)
    }
  },
  async clearCache(chatId:string):Promise<void> {
    chatStateStoreClient.clearCache(chatId)
  }
})

async function getVideoFileSize(url: string) {
  try {
    const res = await axios.head(url, { timeout: 5000 })
    const length = res.headers['content-length']
    return length ? parseInt(length, 10) : null
  } catch (error) {
    logger.warn(`HEAD 请求失败，无法获取视频文件大小: ${error}`)
    return null
  }
}
