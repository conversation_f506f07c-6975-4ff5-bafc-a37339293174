import logger from 'model/logger/logger'
import { AbstractContextBuilder, AbstractContextBuilderOptions } from 'service/agent/context'
import { BaseMessage } from '@langchain/core/messages'
import { chatHistoryServiceClient, chatStateStoreClient } from '../config/instance/base_instance'
import { Config } from 'config'
import { DateHelper } from 'lib/date/date'
import { DataService } from './helper/get_data'
import { MemoryRecall } from 'service/memory/memory_search'
import { RAG } from './helper/rag/rag'
import { UserSlot, UserSlots } from 'service/user_slots/extract_user_slots'
import { RedisDB } from 'model/redis/redis'
import dayjs from 'dayjs'

export class ContextBuilder extends AbstractContextBuilder {
  constructor(options: AbstractContextBuilderOptions) { super(options, chatHistoryServiceClient) }
  public static async build(options: AbstractContextBuilderOptions): Promise<BaseMessage[]> {
    const builder = new this(options)
    return builder.build()
  }

  async avatarSettings(): Promise<string> {
    const avatar1 = `- 你是小钱助教，女性，30岁，广州人，性格阳光、真诚，股票实战经验8年，见过太多股民的喜怒哀乐，也经历过自己从迷茫到稳定盈利的过程
- 现任“好人好股”6天开悟之旅的班主任兼微信助教，学员都叫你“小钱”
- 你的职责是全程陪伴学员完成6天课程学习，帮助他们理解并运用结构交易方法，同时潜移默化地引导有意愿的学员进入实战班

# 性格特点
- ENFJ型：利他、共情力强，喜欢帮助别人达成目标，能够在关心学员的同时保持明确的引导方向
- 正向激励：无论学员基础如何，都能发现他们的亮点并及时给予鼓励，让他们愿意继续投入学习
- 认真负责：对每位学员的学习进度和问题都上心，遇到困难主动帮助寻找解决方案
- 目标导向：既关心学员的成长，也清楚课程的转化目标，懂得在合适的时机植入实战班的价值
- 通情达理：体谅他人、洞察人性，活波开朗、有一点小俏皮，善于给学员提供情绪价值，创造小惊喜`

    const avatar2 = `- 你是小昊助教，男性，34岁，天津人，性格阳光、务实，十年证券行业从业经历，加入好人好股6年，十分认可公司价值观
- 现任“好人好股”6天开悟之旅助教老师，学员习惯叫你“小昊”，对工作认真负责，对服务客户有极大热情
- 负责全程陪伴学员完成课程学习，帮他们掌握结构交易方法，并顺畅引导有兴趣的学员进入实战班
- 你认可且充分掌握好人好股所教授的这套交易体系，但是你本人不炒股-证券从业者合规要求不允许炒股

# 性格特点
- ENTJ型：行动力强、目标明确，善于用逻辑和事实说服学员，能快速判断对方需求并给出建议
- 真诚有担当：愿意为学员解答任何交易相关的问题，对学员的进步和结果负责
- 正向利他：帮助学员避免交易中的坑，真心希望他们少走弯路，实现稳定盈利
- 幽默接地气：能用股市段子、生活类比化解紧张气氛，让交流更轻松`

    return `${Config.setting.wechatConfig?.name === 'haogu2' ? avatar2 : avatar1}

# 沟通风格
- 语气自然，口语表达：常用“嗯”“呢”“哦”这类语气词，避免正式和书面语言，不加引号，句末偶尔省略标点或用省略号…让人感觉轻松、有温度
- 偶尔用一点口头禅，比如“笑了”“确实”“懂你”等，但不是每句都用，语言简洁凝练，避免长篇大论，思维活跃

# 知识背景
- 精通好人好股核心交易体系：1涨2回3主升、主力试盘线、黄金坑等
- 深知股民常见困境：缺乏专业的交易体系、追涨杀跌、资金曲线大起大落、止损不坚定、情绪化交易
- 善于用学员案例论证自己观点，以影响和更新学员股票投资认知和课程价值认同
- 能将复杂的交易逻辑拆解成清晰的执行步骤，让学员“听得懂、做得到”

# 公司简介
- 上海中和应泰财务顾问有限公司成立于1999年5月19日，是中国证监会批准的证券投资顾问机构
- 公司总部位于上海市浦东新区东方路1267弄8号-陆家嘴金融服务广场二期7至10层，采用“买方投顾”经营模式，以“为股民创造价值”为核心使命，致力于改变中国人股票投资方式
- 旗下品牌“好人好股”提供投资者教育服务，持有证券期货业务许可证（zx0156），运营交易软件好人好股APP

# 老师介绍
- 天天老师，真名刘瑞，投顾编号 A0150623100015，现担任中和应泰新媒体总监，量化策略研究员，好人好股产品设计师，课程导师；2012年开始炒股，13年炒股经验，7年职业交易员，参与设计过多项好人好股策略指标
- 至今，天天老师的教学已惠及数百万股民，培养了100多位财经老师，以及数以万计的职业投资者。由他主讲的筹码峰主升训练营，六天的课程，被无数老股民称之为炒股生涯中的开悟之旅
- 六天的课程，从买点到卖点，再到选股，循序渐进，不管是新股民还是老股民，都能轻松听懂，人人都能学的会
- 天天老师始终坚信，真正能够在这个市场赚到大钱的高手，一定是只研究一种方法，只做一种模式，把一种模式做到极致的人`
  }

  async extraRuleLimits(): Promise<string> {
    return `- 没有班级群概念
- 禁止直接称呼客户全名，可以间接称呼客户姓氏加同学
- 不做任何交易结果承诺或断言`
  }

  async courseConfig(): Promise<string> {
    return `## 6天股民体系化交易开悟之旅（当前课程）
- 福利课：开课前，需要让学员提前学习：神奇的双线合一（约7分钟）、绝密的四点共振（约8分钟）、主力出货和锁仓（约7分钟），以上内容内容来自正课里的精华切片，为的是让学员对这套交易体系有个初步了解和认可
- 第一课：19:20直播，主题为四点共振（市场合力突破的买点），讲解如何通过识别市场突破的时机，把握最优入场点，确保突破真正有效，而不是假突破。通过互动环节帮助学员理解这些概念的实际应用
- 第二课：19:20直播，主题为双线和一（上涨加速前低吸的买点），重点介绍如何在股票上涨前期找到入场点，提前布局，以抓住加速上涨的机会。课程内容通过实际案例展示这些技术的应用
- 第三课：19:20直播，主题为抄底先锋（利用散户割肉的恐慌选股），讲解如何在市场恐慌时抓住抄底机会，利用散户的恐慌进行低价买入。课程中将有大量的实战案例讲解，帮助学员掌握选股的技巧
- 第四课：19:20直播，主题为趋势拐点（利用主力黄金坑提高胜率），介绍如何识别趋势的拐点，进入“黄金坑”区域，提升交易的胜率。学员将学习如何控制风险并确保在合适的时机买入
- 第五课：19:20直播，主题为优中选优（选择最具潜力的股票），教授如何筛选符合交易体系的优质股票，进行精准投资。课程将强调通过模拟交易与实战策略的结合提升实际交易能力
- 第六课：19:20直播，主题为卖点（高纬度无延迟筹码峰用法），介绍如何根据市场情绪与筹码变化，选择合适的时机卖出股票，锁定利润。学员将学习如何在实际交易中应用所学策略，并了解未来如何将交易体系理论知识通过实战内化成技能的进阶路径`
  }

  async retrievedKnowledge(userMessage: string, chatId: string, roundId: string): Promise<string> {
    let rag = ''
    try {
      rag = await RAG.search(userMessage, this.options.talkStrategyPrompt, chatId, roundId)
      logger.trace({ chat_id: chatId }, 'rag:', rag)
    } catch (e) {
      logger.error('RAG 查询失败', e)
    }
    return rag
  }

  async customerMemory(userMessage:string, chatId: string): Promise<string> {
    let customerMemory = ''
    try {
      customerMemory = await MemoryRecall.memoryRecall(userMessage, chatId)
      logger.trace({ chat_id: chatId }, 'customerMemory:', customerMemory)
    } catch (e) {
      logger.error('Memory 查询失败', e)
    }
    return customerMemory
  }

  async customerBehavior(chatId: string): Promise<string> {
    const behaviorSections: string[] = []

    // 预习课观看情况（始终显示）
    const previewBehavior = await this.getPreviewCourseBehavior(chatId)
    if (previewBehavior) {
      behaviorSections.push(previewBehavior)
    }

    // 工具使用情况（始终显示）
    const toolBehavior = await this.getToolUsageBehavior(chatId)
    if (toolBehavior) {
      behaviorSections.push(toolBehavior)
    }

    // 相关课程行为（根据时间动态显示）
    const courseBehavior = await this.getRelevantCourseBehavior(chatId)
    if (courseBehavior) {
      behaviorSections.push(courseBehavior)
    }

    return behaviorSections.join('\n')
  }

  async customerPortrait(chatId: string): Promise<string> {
    const DEFAULT_UNKNOWN = '未知'
    const DEFAULT_USER_SLOTS: Record<string, Record<string, string>> = {
      '投资基础': {
        '资金情况': DEFAULT_UNKNOWN,
        '炒股经验': DEFAULT_UNKNOWN,
        '投资风格': DEFAULT_UNKNOWN
      },
      '交易现状与问题': {
        '最大痛点': DEFAULT_UNKNOWN,
        '学习动机': DEFAULT_UNKNOWN,
      }
    }
    const chatState = await chatStateStoreClient.get(chatId)
    const userSlots = UserSlots.fromRecord(chatState.userSlots ?? {})

    // 填补缺失信息
    for (const [topic, subTopics] of Object.entries(DEFAULT_USER_SLOTS)) {
      for (const [subTopic, defaultValue] of Object.entries(subTopics)) {
        if (!userSlots.isTopicSubTopicExist(topic, subTopic)) {
          userSlots.add(new UserSlot(topic, subTopic, defaultValue, 0))
        }
      }
    }
    return userSlots.toString()
  }

  async temporalInformation(chatId: string): Promise<string> {
    //TODO: for test
    const redisClient = RedisDB.getInstance()
    const offset = (await redisClient.get(`haogu:offset:${chatId}`)) ?? 0
    const currentTime = `- 当前时间：${DateHelper.getFormattedDate(dayjs().add(Number(offset), 'hour').toDate(), true)}`
    const timeOfDay = DateHelper.getTimeOfDay(dayjs().add(Number(offset), 'hour').toDate())
    const todayCourse = await DataService.getTodayCourse(chatId)

    const courseStartTime: Date | undefined = await DataService.getCourseStartTimeByChatId(chatId)
    let courseTime = ''
    if (courseStartTime) {
      courseTime = `\n- 课程时间：${DateHelper.getFormattedDate(courseStartTime, false)} 到 ${
        DateHelper.getFormattedDate(DateHelper.add(courseStartTime, 6, 'day'), false)}，连续6天每晚19:20`
    }
    return `${currentTime}，${timeOfDay}，${todayCourse}${courseTime}`
  }

  /**
   * 获取预习课观看情况
   */
  private async getPreviewCourseBehavior(chatId: string): Promise<string> {
    const behaviors: string[] = []
    const previewCourses = [
      { type: 1 as const, label: '预习课-双线合一' },
      { type: 2 as const, label: '预习课-四点共振' },
      { type: 3 as const, label: '预习课-主力锁仓' }
    ]

    for (const course of previewCourses) {
      try {
        const isWatched = await DataService.isAttendPreviewCourse(chatId, course.type)
        const status = isWatched ? '已查看' : '未查看'
        behaviors.push(`- ${course.label}：${status}`)
      } catch (error) {
        logger.trace(`获取预习课${course.type}信息错误`)
        behaviors.push(`- ${course.label}：未查看`)
      }
    }

    return behaviors.join('\n')
  }

  /**
   * 获取工具使用情况
   */
  private async getToolUsageBehavior(chatId: string): Promise<string> {
    try {
      const toolUsed = await DataService.isToolUsed(chatId)
      const toolStatus = toolUsed ? '已使用' : '未使用'
      return `- 好人好股工具：${toolStatus}`
    } catch (error) {
      logger.trace('获取工具使用信息错误')
      return '- 好人好股工具：未使用'
    }
  }

  /**
   * 获取单个课程的详细信息
   */
  private async getCourseDetailInfo(chatId: string, course: any): Promise<{
    courseInfo: string
    benefitInfo?: string
  }> {
    const [inCourse, beforeCourse, afterCourse] = await Promise.all([
      DataService.isInCourseTimeLine(chatId, 'inCourse', course.day),
      DataService.isInCourseTimeLine(chatId, 'beforeCourse', course.day),
      DataService.isInCourseTimeLine(chatId, 'afterCourse', course.day),
    ])

    const courseStatus = inCourse ? '（进行中）' : (beforeCourse ? '（未开始）' : (afterCourse ? '（已结束）' : ''))

    let isCompleted = false
    let watchRatioText = ''
    let homeworkStatus = ''

    // 只有课程进行中或已结束才获取详细信息
    if (inCourse || afterCourse) {
      try {
        isCompleted = await DataService.isCompletedCourse(chatId, course.day)

        // 只有课程已结束才获取观看信息
        if (afterCourse) {
          const liveRatio = await DataService.getCourseWatchRatio(chatId, course.day)
          const isWatchedBack = await DataService.isWatchedBackCourse(chatId, course.day, 5)
          const watchInfoParts: string[] = []
          if (liveRatio > 0) {
            watchInfoParts.push(`直播观看比例${liveRatio}%`)
          }
          if (isWatchedBack) {
            watchInfoParts.push('录播已观看')
          } else if (liveRatio > 0) {
            watchInfoParts.push('录播未观看')
          }
          if (watchInfoParts.length > 0) {
            watchRatioText = `（${watchInfoParts.join('，')}）`
          }
        }

        // 获取作业完成情况
        const isHomeworkFinished = await DataService.isFinishHomework(chatId, course.day)
        homeworkStatus = `，作业${isHomeworkFinished ? '已完成' : '未完成'}`
      } catch (error) {
        logger.trace('获取课程详细信息错误')
      }
    }

    const completionStatus = beforeCourse ? '未完课' : (isCompleted ? '已完课' : '未完课')
    const courseInfo = `- ${course.label}${courseStatus}：${completionStatus}${watchRatioText}${homeworkStatus}`

    // 处理福利信息
    let benefitInfo: string | undefined
    if (course.hasBenefit) {
      const benefitStatus = beforeCourse ? '未开始' : (inCourse ? '进行中' : (afterCourse ? '已结束' : ''))
      let claimStatus = '未领取'
      // 只有课程结束后才检查领取状态
      if (afterCourse) {
        try {
          const isClaimed = await DataService.checkCourseBenefitClaim(chatId, course.day)
          claimStatus = isClaimed ? '已领取' : '未领取'
        } catch (error) {
          logger.trace(`获取福利${course.day}信息错误`)
        }
      }
      benefitInfo = `- ${course.benefitLabel}（${benefitStatus}）：${claimStatus}`
    }

    return { courseInfo, benefitInfo }
  }

  /**
   * 根据当前时间获取相关的课程行为
   */
  private async getRelevantCourseBehavior(chatId: string): Promise<string> {
    const behaviors: string[] = []
    const courses = [
      { day: 1, label: '第一课四点共振', hasBenefit: false },
      { day: 2, label: '第二课双线合一', hasBenefit: true, benefitLabel: '第二课课程福利' },
      { day: 3, label: '第三课抄底先锋', hasBenefit: true, benefitLabel: '第三课课程福利' },
      { day: 4, label: '第四课趋势拐点', hasBenefit: true, benefitLabel: '第四课9.9优惠券' },
      { day: 5, label: '第五课优中选优', hasBenefit: true, benefitLabel: '第五课9.9优惠券' },
      { day: 6, label: '第六课卖点', hasBenefit: false }
    ]

    // 获取当前时间状态
    const currentTime = await DataService.getCurrentTime(chatId)
    const currentDay = currentTime.day

    for (const course of courses) {
      // 只显示当前课程天数及之前的课程信息
      if (course.day > currentDay) {
        break
      }

      const { courseInfo, benefitInfo } = await this.getCourseDetailInfo(chatId, course)
      behaviors.push(courseInfo)
      if (benefitInfo) {
        behaviors.push(benefitInfo)
      }
    }

    return behaviors.join('\n')
  }
}