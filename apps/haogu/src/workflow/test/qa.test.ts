import { UUID } from 'lib/uuid/uuid'
import { Workflow } from '../workflow' // 导入下方提供的 JSON 数据
import { when } from 'jest-when'
import { DataService } from '../helper/get_data'
import { Config } from 'config'
import { chatHistoryServiceClient } from '../../config/instance/base_instance'
import { FileHelper } from 'lib/file'
import path from 'path'
import fs from 'fs'
import { catchError } from 'lib/error/catchError'

const collectedChatIds: any[] = [] // 用来收集所有生成的 chat_id

describe('AI Bot Automated Test Suite', function () {
  beforeAll(() => {
    Config.setting.projectName = 'haogu'
  })

  beforeEach(() => {

    // 重置 spy 的实现，再设置默认返回（未指定 chatId 时使用）
    getCurrentTimeMock.mockReset()
    getCurrentTimeMock.mockResolvedValue({ day: 0, time: '00:00:00' })
  })

  const file = fs.readFileSync(path.join(process.cwd(), 'dev', 'test_suite.json'), 'utf8')
  const TestSuite = JSON.parse(file)

  const getCurrentTimeMock = jest
    .spyOn(DataService, 'getCurrentTime')
    .mockResolvedValue({ day: 0, time: '00:00:00' })

  for (const categoryName in TestSuite) {
    const testCases = TestSuite[categoryName]

    describe(`Category: ${categoryName}`, function () {
      for (const testCase of testCases) {
        const isMultiTurn = 'turns' in testCase
        const testName = isMultiTurn ? testCase.scenario : testCase.query_text

        const executeTest = async (timeMockLabel) => {
          // 先生成 userId 和 chatId
          const userId = UUID.short()
          const test_chat_id = `test${timeMockLabel ?? ''}_${userId}`

          if (timeMockLabel === 'Day0') {
            when(getCurrentTimeMock)
              .calledWith(test_chat_id)
              .mockResolvedValue({ day: 0, time: '08:00:00' })
          } else if (timeMockLabel === 'Day6') {
            when(getCurrentTimeMock)
              .calledWith(test_chat_id)
              .mockResolvedValue({ day: 6, time: '23:00:00' })
          }

          // 2. 执行对话
          if (isMultiTurn) {
            for (const turn of testCase.turns) {
              await Workflow.step(test_chat_id, userId, turn.formatted_input)
            }
          } else {
            await Workflow.step(test_chat_id, userId, testCase.formatted_input)
          }

          // 3. 收集 chat_id
          collectedChatIds.push({
            name: testName,
            chat_id: test_chat_id,
            time: timeMockLabel
          })

          console.log(
            `Executed Test: ${testName}, Chat ID: ${test_chat_id}, TimeContext: ${timeMockLabel}`
          )
        }

        it.concurrent(`should handle: ${testName}`, async () => {
          const runs = testCase.time_sensitive ? ['Day6'] : [null]
          await Promise.all(runs.map((label) => executeTest(label)))
        }, 300000)
      }
    })
  }

  afterAll(async () => {
    console.log('=== All Collected Chat IDs ===')
    console.log(JSON.stringify(collectedChatIds, null, 2))

    const resultFile = path.join(process.cwd(), 'dev', 'qa_test_result.txt')

    await catchError(FileHelper.removeFile(resultFile))
    await FileHelper.touchFile(resultFile)

    for (const collectedChatId of collectedChatIds) {
      const chat_history = await chatHistoryServiceClient.getFormatChatHistoryByChatId(collectedChatId.chat_id)

      console.log(chat_history) // append 文件中
      await FileHelper.appendFile(resultFile, `${chat_history}\nhttp://116.62.164.13:3000/haogu/user/chat/${collectedChatId.chat_id}\n\n`)
    }
  })
})