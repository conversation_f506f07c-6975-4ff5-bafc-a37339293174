'use client'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { HaoguUserData } from '../../type/user'
import { IChattingFlag } from 'haogu/src/config/manifest'

export function ChatEdit<T>({
  chat,
  changeCourseNo,
  changeNextStage,
  stageOption,
  updateIsPaid,
  clearCache,
  resetSop,
  updateIsAttendCourse,
  updateIsCompleteCourse,
  sendFinishHomeworkEvent,
  getOffset,
  setOffset,
  sendOrderEvent,
  startBigPlanner,
  updateBooleanField,
  updateWatchRatio,
  freeKick
}: {
  chat:HaoguUserData,
  changeNextStage(chatId: string, stage: T): Promise<void>,
  changeCourseNo(chatId: string, courseNo: number): Promise<void>,
  clearCache(chatId:string):Promise<void>
  updateIsPaid(chatId:string, isPaid:boolean): Promise<void>
  resetSop(chatId:string): Promise<void>
  updateIsAttendCourse(chatId: string, day: number, isAttend: boolean): Promise<void>
  updateIsCompleteCourse(chatId: string, day: number, isAttend: boolean): Promise<void>
  sendOrderEvent(chatId: string): Promise<void>
  sendFinishHomeworkEvent(chatId: string, day: number, score: number): Promise<void>
  setOffset(chatId: string, offset: number): Promise<void>
  getOffset(chatId:string):Promise<number>
  startBigPlanner(chatId:string):Promise<void>
  updateBooleanField(chatId: string, fieldName: string, value: boolean): Promise<void>
  updateWatchRatio(chatId: string, day: number, ratio: number): Promise<void>
  freeKick(chatId: string, task: string): Promise<void>
  stageOption:string[]
}) {
  const [stage, setStage] = useState<string>(chat.chat_state.nextStage)
  const [courseNo, setCourseNo] = useState<string> (`${chat.course_no ?? ''}`)
  const [isPaid, setIsPaid] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_payment ?? false)
  const [loading, setLoading] = useState<boolean>(false)
  const [isAttendCourseDay1, setIsAttendCourseDay1] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_course_day1 ?? false)
  const [isAttendCourseDay2, setIsAttendCourseDay2] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_course_day2 ?? false)
  const [isAttendCourseDay3, setIsAttendCourseDay3] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_course_day3 ?? false)
  const [isAttendCourseDay4, setIsAttendCourseDay4] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_course_day4 ?? false)
  const [isAttendCourseDay5, setIsAttendCourseDay5] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_course_day5 ?? false)
  const [isAttendCourseDay6, setIsAttendCourseDay6] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_course_day6 ?? false)
  const [isCompleteCourseDay1, setIsCompleteCourseDay1] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_course_day1 ?? false)
  const [isCompleteCourseDay2, setIsCompleteCourseDay2] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_course_day2 ?? false)
  const [isCompleteCourseDay3, setIsCompleteCourseDay3] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_course_day3 ?? false)
  const [isCompleteCourseDay4, setIsCompleteCourseDay4] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_course_day4 ?? false)
  const [isCompleteCourseDay5, setIsCompleteCourseDay5] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_course_day5 ?? false)
  const [isCompleteCourseDay6, setIsCompleteCourseDay6] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_course_day6 ?? false)
  const [isToolUsed, setIsToolUsed] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_tool_used ?? false)
  const [isAttendPreviewCourse1, setIsAttendPreviewCourse1] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_preview_course_1 ?? false)
  const [isAttendPreviewCourse2, setIsAttendPreviewCourse2] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_preview_course_2 ?? false)
  const [isAttendPreviewCourse3, setIsAttendPreviewCourse3] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_attend_preview_course_3 ?? false)
  const [isBenefitClaimedDay2, setIsBenefitClaimedDay2] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_benefit_claimed_day2 ?? false)
  const [isBenefitClaimedDay3, setIsBenefitClaimedDay3] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_benefit_claimed_day3 ?? false)
  const [isBenefitClaimedDay4, setIsBenefitClaimedDay4] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_benefit_claimed_day4 ?? false)
  const [isBenefitClaimedDay5, setIsBenefitClaimedDay5] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_benefit_claimed_day5 ?? false)
  const [liveWatchRatioDay1, setLiveWatchRatioDay1] = useState<number>((chat.chat_state.state as IChattingFlag).live_watch_ratio_day1 ?? 0)
  const [liveWatchRatioDay2, setLiveWatchRatioDay2] = useState<number>((chat.chat_state.state as IChattingFlag).live_watch_ratio_day2 ?? 0)
  const [liveWatchRatioDay3, setLiveWatchRatioDay3] = useState<number>((chat.chat_state.state as IChattingFlag).live_watch_ratio_day3 ?? 0)
  const [liveWatchRatioDay4, setLiveWatchRatioDay4] = useState<number>((chat.chat_state.state as IChattingFlag).live_watch_ratio_day4 ?? 0)
  const [liveWatchRatioDay5, setLiveWatchRatioDay5] = useState<number>((chat.chat_state.state as IChattingFlag).live_watch_ratio_day5 ?? 0)
  const [liveWatchRatioDay6, setLiveWatchRatioDay6] = useState<number>((chat.chat_state.state as IChattingFlag).live_watch_ratio_day6 ?? 0)
  const [isWatchedBackCourseDay1, setIsWatchedBackCourseDay1] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_watched_back_course_day1 ?? false)
  const [isWatchedBackCourseDay2, setIsWatchedBackCourseDay2] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_watched_back_course_day2 ?? false)
  const [isWatchedBackCourseDay3, setIsWatchedBackCourseDay3] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_watched_back_course_day3 ?? false)
  const [isWatchedBackCourseDay4, setIsWatchedBackCourseDay4] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_watched_back_course_day4 ?? false)
  const [isWatchedBackCourseDay5, setIsWatchedBackCourseDay5] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_watched_back_course_day5 ?? false)
  const [isWatchedBackCourseDay6, setIsWatchedBackCourseDay6] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_watched_back_course_day6 ?? false)
  const [homeworkDay, setHomeworkDay] = useState<number>(1)
  const [homeworkScore, setHomeworkScore] = useState<number>(100)
  const [offset, setPublicOffset] = useState<number>(0)
  const [freeKickTask, setFreeKickTask] = useState<string>('')

  useEffect(() => {
    getOffset(chat.id).then((res) => {
      setPublicOffset(res)
    })
  }, [])


  return <div>
    <div className='text-2xl p-2'>编辑: {chat.contact.wx_name}</div>
    <form className='flex gap-2 items-center' onSubmit={(e) => {
      e.preventDefault()
      setLoading(true)
      toast.promise(changeNextStage(chat.id, stage as T), {
        pending: 'change next stage pending',
        success: 'change next stage success',
        error: {
          render:(e) => {
            return `error: ${e.data}`
          }
        }
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <label className='label w-40'>切换阶段</label>
      <select className='select focus-within:outline-0' disabled={loading} value={stage} onChange={(e) => {
        setStage(e.currentTarget.value)
      }}>
        {stageOption.map((item, index) => {
          return <option key={index}>{item}</option>
        })}
      </select>
      <button type="submit" className='btn disabled:btn-disabled' disabled={loading}>切换</button>
    </form>
    <form className='flex gap-2 items-center mt-2' onSubmit={(e) => {
      e.preventDefault()
      setLoading(true)
      toast.promise(changeCourseNo(chat.id, Number(courseNo)), {
        pending: 'change courseNo pending',
        success: 'change courseNo success',
        error: {
          render:(e) => {
            return `error: ${e.data}`
          }
        }
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <label className='label w-40'>修改课程号</label>
      <input type="number" className='input focus-within:outline-0' value={courseNo} onChange={(e) => { setCourseNo(e.currentTarget.value) }}/>
      <button type="submit" className='btn disabled:btn-disabled' disabled={loading}>修改</button>
    </form>
    <div className='flex gap-2 items-center mt-2' >
      <label className='label w-40'>修改成单状态</label>
      <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isPaid} disabled={loading} onChange={(e) => {
        e.preventDefault()
        const checked = e.currentTarget.checked
        setLoading(true)
        toast.promise(async() => {
          await updateIsPaid(chat.id, e.currentTarget.checked)
          await clearCache(chat.id)
        }, {
          pending: 'change payment state pending',
          success: 'change payment state success',
          error: {
            render:(e) => {
              return `error: ${e.data}`
            }
          }
        }).then(() => {
          setIsPaid(checked)
        }).finally(() => {
          setLoading(false)
        })
      }}/>
    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第一天到课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendCourseDay1} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsAttendCourse(chat.id, 1, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendCourseDay1(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第一天完课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isCompleteCourseDay1} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsCompleteCourse(chat.id, 1, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            if (checked) {
              setIsAttendCourseDay1(checked)
            }
            setIsCompleteCourseDay1(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>

    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第二天到课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendCourseDay2} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsAttendCourse(chat.id, 2, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendCourseDay2(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第二天完课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isCompleteCourseDay2} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsCompleteCourse(chat.id, 2, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            if (checked) {
              setIsAttendCourseDay2(checked)
            }
            setIsCompleteCourseDay2(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>

    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第三天到课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendCourseDay3} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsAttendCourse(chat.id, 3, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendCourseDay3(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第三天完课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isCompleteCourseDay3} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsCompleteCourse(chat.id, 3, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            if (checked) {
              setIsAttendCourseDay3(checked)
            }
            setIsCompleteCourseDay3(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>

    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第四天到课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendCourseDay4} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsAttendCourse(chat.id, 4, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendCourseDay4(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第四天完课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isCompleteCourseDay4} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsCompleteCourse(chat.id, 4, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            if (checked) {
              setIsAttendCourseDay4(checked)
            }
            setIsCompleteCourseDay4(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>

    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第五天到课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendCourseDay5} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsAttendCourse(chat.id, 5, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendCourseDay5(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第五天完课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isCompleteCourseDay5} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsCompleteCourse(chat.id, 5, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            if (checked) {
              setIsAttendCourseDay5(checked)
            }
            setIsCompleteCourseDay5(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>

    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第六天到课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendCourseDay6} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsAttendCourse(chat.id, 6, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendCourseDay6(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>修改第六天完课</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isCompleteCourseDay6} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateIsCompleteCourse(chat.id, 6, checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            if (checked) {
              setIsAttendCourseDay6(checked)
            }
            setIsCompleteCourseDay6(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>

    </div>
    <div className='flex gap-2 items-center mt-2' >
      <label className='label w-40'>好人好股软件使用</label>
      <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isToolUsed} disabled={loading} onChange={(e) => {
        e.preventDefault()
        const checked = e.currentTarget.checked
        setLoading(true)
        toast.promise(async() => {
          await updateBooleanField(chat.id, 'is_tool_used', checked)
        }, {
          pending: 'change state pending',
          success: 'change state success',
          error: {
            render:(e) => {
              return `error: ${e.data}`
            }
          }
        }).then(() => {
          setIsToolUsed(checked)
        }).finally(() => {
          setLoading(false)
        })
      }}/>
    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>预习课1-双线合一</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendPreviewCourse1} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateBooleanField(chat.id, 'is_attend_preview_course_1', checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendPreviewCourse1(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>预习课2-四点共振</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendPreviewCourse2} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateBooleanField(chat.id, 'is_attend_preview_course_2', checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendPreviewCourse2(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>预习课3-主力锁仓</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isAttendPreviewCourse3} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateBooleanField(chat.id, 'is_attend_preview_course_3', checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsAttendPreviewCourse3(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第二课课程福利领取</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isBenefitClaimedDay2} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateBooleanField(chat.id, 'is_benefit_claimed_day2', checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsBenefitClaimedDay2(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第三课课程福利领取</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isBenefitClaimedDay3} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateBooleanField(chat.id, 'is_benefit_claimed_day3', checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsBenefitClaimedDay3(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第四课9.9优惠券领取</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isBenefitClaimedDay4} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateBooleanField(chat.id, 'is_benefit_claimed_day4', checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsBenefitClaimedDay4(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第五课9.9优惠券领取</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isBenefitClaimedDay5} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateBooleanField(chat.id, 'is_benefit_claimed_day5', checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsBenefitClaimedDay5(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
    </div>

    <div className='text-lg font-bold mt-4 mb-2'>观看比例设置</div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第一课观看比例(%)</label>
        <input type="number" className='input w-20 focus-within:outline-0' value={liveWatchRatioDay1} min={0} max={100} disabled={loading} onChange={(e) => {
          setLiveWatchRatioDay1(e.currentTarget.valueAsNumber || 0)
        }} />
        <button className='btn btn-sm disabled:btn-disabled' disabled={loading} onClick={() => {
          setLoading(true)
          toast.promise(updateWatchRatio(chat.id, 1, liveWatchRatioDay1), {
            pending: 'updating ratio',
            success: 'ratio updated',
            error: 'update failed'
          }).finally(() => {
            setLoading(false)
          })
        }}>设置</button>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第二课观看比例(%)</label>
        <input type="number" className='input w-20 focus-within:outline-0' value={liveWatchRatioDay2} min={0} max={100} disabled={loading} onChange={(e) => {
          setLiveWatchRatioDay2(e.currentTarget.valueAsNumber || 0)
        }} />
        <button className='btn btn-sm disabled:btn-disabled' disabled={loading} onClick={() => {
          setLoading(true)
          toast.promise(updateWatchRatio(chat.id, 2, liveWatchRatioDay2), {
            pending: 'updating ratio',
            success: 'ratio updated',
            error: 'update failed'
          }).finally(() => {
            setLoading(false)
          })
        }}>设置</button>
      </div>
    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第三课观看比例(%)</label>
        <input type="number" className='input w-20 focus-within:outline-0' value={liveWatchRatioDay3} min={0} max={100} disabled={loading} onChange={(e) => {
          setLiveWatchRatioDay3(e.currentTarget.valueAsNumber || 0)
        }} />
        <button className='btn btn-sm disabled:btn-disabled' disabled={loading} onClick={() => {
          setLoading(true)
          toast.promise(updateWatchRatio(chat.id, 3, liveWatchRatioDay3), {
            pending: 'updating ratio',
            success: 'ratio updated',
            error: 'update failed'
          }).finally(() => {
            setLoading(false)
          })
        }}>设置</button>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第四课观看比例(%)</label>
        <input type="number" className='input w-20 focus-within:outline-0' value={liveWatchRatioDay4} min={0} max={100} disabled={loading} onChange={(e) => {
          setLiveWatchRatioDay4(e.currentTarget.valueAsNumber || 0)
        }} />
        <button className='btn btn-sm disabled:btn-disabled' disabled={loading} onClick={() => {
          setLoading(true)
          toast.promise(updateWatchRatio(chat.id, 4, liveWatchRatioDay4), {
            pending: 'updating ratio',
            success: 'ratio updated',
            error: 'update failed'
          }).finally(() => {
            setLoading(false)
          })
        }}>设置</button>
      </div>
    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第五课观看比例(%)</label>
        <input type="number" className='input w-20 focus-within:outline-0' value={liveWatchRatioDay5} min={0} max={100} disabled={loading} onChange={(e) => {
          setLiveWatchRatioDay5(e.currentTarget.valueAsNumber || 0)
        }} />
        <button className='btn btn-sm disabled:btn-disabled' disabled={loading} onClick={() => {
          setLoading(true)
          toast.promise(updateWatchRatio(chat.id, 5, liveWatchRatioDay5), {
            pending: 'updating ratio',
            success: 'ratio updated',
            error: 'update failed'
          }).finally(() => {
            setLoading(false)
          })
        }}>设置</button>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第六课观看比例(%)</label>
        <input type="number" className='input w-20 focus-within:outline-0' value={liveWatchRatioDay6} min={0} max={100} disabled={loading} onChange={(e) => {
          setLiveWatchRatioDay6(e.currentTarget.valueAsNumber || 0)
        }} />
        <button className='btn btn-sm disabled:btn-disabled' disabled={loading} onClick={() => {
          setLoading(true)
          toast.promise(updateWatchRatio(chat.id, 6, liveWatchRatioDay6), {
            pending: 'updating ratio',
            success: 'ratio updated',
            error: 'update failed'
          }).finally(() => {
            setLoading(false)
          })
        }}>设置</button>
      </div>
    </div>

    <div className='text-lg font-bold mt-4 mb-2'>录播观看设置</div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第一课录播观看</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isWatchedBackCourseDay1} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateBooleanField(chat.id, 'is_watched_back_course_day1', checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsWatchedBackCourseDay1(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第二课录播观看</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isWatchedBackCourseDay2} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateBooleanField(chat.id, 'is_watched_back_course_day2', checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsWatchedBackCourseDay2(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第三课录播观看</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isWatchedBackCourseDay3} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateBooleanField(chat.id, 'is_watched_back_course_day3', checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsWatchedBackCourseDay3(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第四课录播观看</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isWatchedBackCourseDay4} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateBooleanField(chat.id, 'is_watched_back_course_day4', checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsWatchedBackCourseDay4(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
    </div>
    <div className='flex gap-4'>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第五课录播观看</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isWatchedBackCourseDay5} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateBooleanField(chat.id, 'is_watched_back_course_day5', checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsWatchedBackCourseDay5(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-2 items-center mt-2' >
        <label className='label w-40'>第六课录播观看</label>
        <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isWatchedBackCourseDay6} disabled={loading} onChange={(e) => {
          e.preventDefault()
          const checked = e.currentTarget.checked
          setLoading(true)
          toast.promise(async() => {
            await updateBooleanField(chat.id, 'is_watched_back_course_day6', checked)
          }, {
            pending: 'change state pending',
            success: 'change state success',
            error: {
              render:(e) => {
                return `error: ${e.data}`
              }
            }
          }).then(() => {
            setIsWatchedBackCourseDay6(checked)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
    </div>

    <div className='mt-2 flex items-center gap-4'>
      <div className='flex items-center gap-2'>
        <div className='flex gap-2 items-center'>
          <label htmlFor="" className='label'>day</label>
          <input type="number" className='input w-20' max={6} min={1} step={1} value={homeworkDay} onChange={(e) => {
            setHomeworkDay(e.currentTarget.valueAsNumber)
          }} />
        </div>
        <div className='flex gap-2 items-center'>
          <label htmlFor="" className='label'>score</label>
          <input type="number" className='input w-20' max={100} min={0} step={1} value={homeworkScore} onChange={(e) => {
            setHomeworkScore(e.currentTarget.valueAsNumber)
          }} />
        </div>
      </div>
      <button className='btn btn-neutral focus-within:outline-0 disabled:btn-disabled' disabled={loading} onClick={() => {
        setLoading(true)
        toast.promise(sendFinishHomeworkEvent(chat.id, homeworkDay, homeworkScore), {
          pending: 'pending',
          success: 'success',
          error: 'error'
        }).finally(() => {
          setLoading(false)
        })
      }}>do homework</button>
    </div>
    <div className='mt-2 flex items-center gap-4'>
      <div className='flex gap-2 items-center'>
        <label htmlFor="" className='label'>offset</label>
        <input type="number" className='input w-20' max={1000} min={-1000} step={1} value={offset} onChange={(e) => {
          setPublicOffset(e.currentTarget.valueAsNumber)
        }} />
      </div>
      <button className='btn btn-neutral focus-within:outline-0 disabled:btn-disabled' disabled={loading} onClick={() => {
        setLoading(true)
        toast.promise(setOffset(chat.id, offset), {
          pending: 'pending',
          success: 'success',
          error: 'error'
        }).finally(() => {
          setLoading(false)
        })
      }}>set</button>
    </div>
    <div className='flex gap-2 mt-2'>
      <button className='btn btn-neutral focus-within:outline-0 disabled:btn-disabled' disabled={loading} onClick={() => {
        setLoading(true)
        toast.promise(sendOrderEvent(chat.id), {
          pending: 'pending',
          success: 'success',
          error: 'error'
        }).finally(() => {
          setLoading(false)
        })
      }}>place an order</button>
      <button className='btn btn-neutral focus-within:outline-0 disabled:btn-disabled' disabled={loading} onClick={() => {
        setLoading(true)
        toast.promise(startBigPlanner(chat.id), {
          pending: 'pending',
          success: 'success',
          error: 'error'
        }).finally(() => {
          setLoading(false)
        })
      }}>start big planner</button>

    </div>
    <div className='flex gap-2 mt-2'>
      <button className='btn btn-neutral focus-within:outline-0 disabled:btn-disabled' disabled={loading} onClick={() => {
        setLoading(true)
        toast.promise(clearCache(chat.id), {
          pending: 'clear pending',
          success: 'clear success',
          error: 'clear error'
        }).finally(() => {
          setLoading(false)
        })
      }}>clear cache</button>
      <button className='btn btn-neutral focus-within:outline-0 disabled:btn-disabled' disabled={loading} onClick={() => {
        setLoading(true)
        toast.promise(resetSop(chat.id), {
          pending: 'reset pending',
          success: 'reset success',
          error: 'reset error'
        }).finally(() => {
          setLoading(false)
        })
      }}>reset sop</button>
    </div>
    <div className='divider'/>
    <div className='flex items-center gap-2 mt-2'>
      <label className='label w-40'>FreeKick 任务描述</label>
      <input
        type='text'
        className='input flex-1 focus-within:outline-0'
        placeholder='请输入要执行的 FreeKick 任务描述'
        value={freeKickTask}
        disabled={loading}
        onChange={(e) => setFreeKickTask(e.currentTarget.value)}
      />
      <button
        className='btn btn-primary disabled:btn-disabled'
        disabled={loading || freeKickTask.trim().length === 0}
        onClick={() => {
          setLoading(true)
          const task = freeKickTask.trim()
          toast.promise(
            freeKick(chat.id, task),
            {
              pending: 'FreeKick pending',
              success: 'FreeKick triggered',
              error: 'FreeKick error'
            }
          ).then(() => {
            setFreeKickTask('')
          }).finally(() => {
            setLoading(false)
          })
        }}
      >
        触发 FreeKick
      </button>
    </div>
  </div>
}
